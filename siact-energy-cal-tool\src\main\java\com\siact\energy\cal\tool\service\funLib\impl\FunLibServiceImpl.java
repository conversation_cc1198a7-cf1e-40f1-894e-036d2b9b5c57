package com.siact.energy.cal.tool.service.funLib.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.siact.energy.cal.common.pojo.dto.funLib.FunLibSelectQueryDTO;
import com.siact.energy.cal.common.pojo.vo.common.FunLibSelectOptionVO;
import com.siact.energy.cal.tool.convertor.funLib.FunLibConvertor;
import com.siact.energy.cal.tool.dao.funLib.FunLibDao;
import com.siact.energy.cal.tool.service.funLib.FunLibService;
import com.siact.energy.cal.tool.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

import com.siact.energy.cal.tool.entity.funLib.FunLib;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.vo.funLib.FunLibVO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibQueryDTO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibInsertDTO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibUpdateDTO;
import com.siact.energy.cal.common.util.utils.ClassUtil;

/**
 * 常用函数库(FunLib)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-11 09:47:21
 */
@Service("funLibService")
public class FunLibServiceImpl extends ServiceImpl<FunLibDao, FunLib> implements FunLibService {

    @Autowired
    private BaseService baseService;

    @Override
    public PageBean<FunLibVO> listPage(PageBean<FunLibVO> page, FunLibQueryDTO funLibQueryDTO) {
        return page(page, funLibQueryDTO);
    }

    @Override
    public FunLibVO getVoById(Serializable id) {
        FunLib funLib = getById(id);
        return FunLibConvertor.INSTANCE.entity2Vo(funLib);
    }

    @Override
    public Boolean save(FunLibInsertDTO funLibInsertDTO) {
        FunLib funLib = FunLibConvertor.INSTANCE.insertDTO2Entity(funLibInsertDTO);

        // 设置用户信息
        Long currentUserId = getCurrentUserId();
        funLib.setCreator(currentUserId);
        funLib.setUpdater(currentUserId);

        return save(funLib);
    }

    @Override
    public Boolean updateVoById(FunLibUpdateDTO funLibUpdateDTO) {
        FunLib funLib = FunLibConvertor.INSTANCE.updateDTO2Entity(funLibUpdateDTO);

        // 设置更新者信息
        Long currentUserId = getCurrentUserId();
        funLib.setUpdater(currentUserId);

        return updateById(funLib);
    }

    @Override
    public List<FunLibSelectOptionVO> selectList(FunLibSelectQueryDTO dto) {

        LambdaQueryWrapper<FunLib> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(dto.getCalType()), FunLib::getCalType, dto.getCalType());

        return FunLibConvertor.INSTANCE.entity2SelectVO(list(queryWrapper));
    }

    /**
     * 分页查询
     *
     * @param page           分页对象
     * @param funLibQueryDTO 查询实体
     * @return 分页数据
     */
    private PageBean<FunLibVO> page(PageBean<FunLibVO> page, FunLibQueryDTO funLibQueryDTO) {

        // 转换器
        FunLibConvertor convertor = FunLibConvertor.INSTANCE;
        // VO转实体
        FunLib funLib = convertor.queryDTO2Entity(funLibQueryDTO);

        // 创建查询对象
        LambdaQueryWrapper<FunLib> queryWrapper = new LambdaQueryWrapper<>(funLib);

        List<String> voFieldNameList = ClassUtil.getClassAllFields(FunLibVO.class);
        queryWrapper.select(c -> voFieldNameList.contains(c.getProperty()));

        // 查询实体数据
        Page<FunLib> entityPage = page(convertor.voPageBean2EntityPage(page), queryWrapper);

        // 实体分页转VO分页
        PageBean<FunLibVO> voPageBean = convertor.entityPage2VoPageBean(entityPage);

        return voPageBean;
    }

    /**
     * 获取当前登录用户ID
     * 通过BaseService调用权限SDK获取用户信息
     *
     * @return 当前用户ID，如果无法获取则返回默认值1L
     */
    private Long getCurrentUserId() {
        try {
            // 通过反射调用getLoginUser方法
            java.lang.reflect.Method getLoginUserMethod = baseService.getClass().getMethod("getLoginUser");
            Object userDetailVO = getLoginUserMethod.invoke(baseService);

            if (userDetailVO == null) {
                return 1L;
            }

            // 尝试通过反射获取用户ID
            return extractUserId(userDetailVO);
        } catch (Exception e) {
            return 1L;
        }
    }

    /**
     * 从UserDetailVO对象中提取用户ID
     * 尝试多种可能的字段名
     */
    private Long extractUserId(Object userDetailVO) {
        try {
            Class<?> clazz = userDetailVO.getClass();

            // 尝试常见的用户ID getter方法名
            String[] possibleMethodNames = {"getId", "getUserId", "getUser_id", "getID", "getUSER_ID"};

            // 先尝试通过getter方法获取
            for (String methodName : possibleMethodNames) {
                try {
                    java.lang.reflect.Method method = clazz.getMethod(methodName);
                    Object result = method.invoke(userDetailVO);
                    if (result != null) {
                        if (result instanceof Long) {
                            return (Long) result;
                        } else if (result instanceof Integer) {
                            return ((Integer) result).longValue();
                        } else if (result instanceof String) {
                            return Long.valueOf((String) result);
                        }
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个方法
                }
            }

            return 1L;
        } catch (Exception e) {
            return 1L;
        }
    }

}

