package com.siact.energy.cal.common.datasource.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 数据库兼容性工具类
 * 用于检测数据库类型并提供相应的兼容性支持
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Component
public class DatabaseCompatibilityUtil {

    @Autowired
    private DataSource dataSource;

    /**
     * 数据库类型枚举
     */
    public enum DatabaseType {
        MYSQL("mysql"),
        POSTGRESQL("postgresql"),
        ORACLE("oracle"),
        SQLSERVER("sqlserver"),
        KINGBASE("kingbase"),
        DM("dm"),
        UNKNOWN("unknown");

        private final String name;

        DatabaseType(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 检测当前数据库类型
     */
    public DatabaseType detectDatabaseType() {
        try (Connection connection = dataSource.getConnection()) {
            String url = connection.getMetaData().getURL().toLowerCase();
            String driverName = connection.getMetaData().getDriverName().toLowerCase();
            String productName = connection.getMetaData().getDatabaseProductName().toLowerCase();

            if (url.contains("mysql") || driverName.contains("mysql")) {
                return DatabaseType.MYSQL;
            } else if (url.contains("kingbase") || driverName.contains("kingbase") || productName.contains("kingbase")) {
                return DatabaseType.KINGBASE;
            } else if (productName.contains("gbase") || productName.contains("南大通用")) {
                // 南大通用数据库，虽然使用PostgreSQL驱动，但归类为PostgreSQL
                return DatabaseType.POSTGRESQL;
            } else if (url.contains("postgresql") || driverName.contains("postgresql") || productName.contains("postgresql")) {
                return DatabaseType.POSTGRESQL;
            } else if (url.contains("oracle") || driverName.contains("oracle")) {
                return DatabaseType.ORACLE;
            } else if (url.contains("sqlserver") || driverName.contains("sqlserver")) {
                return DatabaseType.SQLSERVER;
            } else if (url.contains("dm") || driverName.contains("dm") || productName.contains("dm dbms")) {
                return DatabaseType.DM;
            } else {
                return DatabaseType.UNKNOWN;
            }
        } catch (SQLException e) {
            throw new RuntimeException("Failed to detect database type", e);
        }
    }

    /**
     * 判断是否为MySQL数据库
     */
    public boolean isMysql() {
        return detectDatabaseType() == DatabaseType.MYSQL;
    }

    /**
     * 判断是否为PostgreSQL系列数据库（包括南大通用）
     */
    public boolean isPostgreSQL() {
        DatabaseType type = detectDatabaseType();
        return type == DatabaseType.POSTGRESQL || type == DatabaseType.KINGBASE;
    }

    /**
     * 判断是否为Oracle数据库
     */
    public boolean isOracle() {
        return detectDatabaseType() == DatabaseType.ORACLE;
    }

    /**
     * 判断是否为SQL Server数据库
     */
    public boolean isSqlServer() {
        return detectDatabaseType() == DatabaseType.SQLSERVER;
    }

    /**
     * 判断是否为达梦数据库
     */
    public boolean isDM() {
        return detectDatabaseType() == DatabaseType.DM;
    }

    /**
     * 判断是否支持ON DUPLICATE KEY UPDATE语法
     */
    public boolean supportsOnDuplicateKeyUpdate() {
        return isMysql();
    }

    /**
     * 判断是否支持ON CONFLICT语法（PostgreSQL系列）
     */
    public boolean supportsOnConflict() {
        return isPostgreSQL();
    }

    /**
     * 判断是否支持MERGE语法（Oracle、SQL Server）
     */
    public boolean supportsMerge() {
        return isOracle() || isSqlServer();
    }

    /**
     * 获取数据库产品名称
     */
    public String getDatabaseProductName() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.getMetaData().getDatabaseProductName();
        } catch (SQLException e) {
            return "Unknown";
        }
    }

    /**
     * 获取数据库产品版本
     */
    public String getDatabaseProductVersion() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.getMetaData().getDatabaseProductVersion();
        } catch (SQLException e) {
            return "Unknown";
        }
    }

    /**
     * 获取JDBC驱动名称
     */
    public String getDriverName() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.getMetaData().getDriverName();
        } catch (SQLException e) {
            return "Unknown";
        }
    }

    /**
     * 获取JDBC驱动版本
     */
    public String getDriverVersion() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.getMetaData().getDriverVersion();
        } catch (SQLException e) {
            return "Unknown";
        }
    }

    /**
     * 打印数据库信息（用于调试）
     */
    public void printDatabaseInfo() {
        System.out.println("=== Database Information ===");
        System.out.println("Database Type: " + detectDatabaseType().getName());
        System.out.println("Database Product: " + getDatabaseProductName());
        System.out.println("Database Version: " + getDatabaseProductVersion());
        System.out.println("Driver Name: " + getDriverName());
        System.out.println("Driver Version: " + getDriverVersion());
        System.out.println("Supports ON DUPLICATE KEY UPDATE: " + supportsOnDuplicateKeyUpdate());
        System.out.println("Supports ON CONFLICT: " + supportsOnConflict());
        System.out.println("Supports MERGE: " + supportsMerge());
        System.out.println("============================");
    }
}
