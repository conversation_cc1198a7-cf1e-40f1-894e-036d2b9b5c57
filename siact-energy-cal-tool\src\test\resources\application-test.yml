# 测试环境配置
server:
  port: 8081

spring:
  application:
    name: siact-energy-cal-tool-test

  # 允许Bean定义覆盖，避免测试环境下的Bean冲突
  main:
    allow-bean-definition-overriding: true

  # 数据源配置选项1 - 使用H2内存数据库进行测试（已切换到MySQL）
  # datasource:
  #   driver-class-name: org.h2.Driver
  #   url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
  #   username: sa
  #   password:

  # 数据源配置选项2 - 使用MySQL进行测试（当前使用）
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************************
    username: root
    password: root
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      pool-name: TestHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # H2数据库控制台（可选，用于调试）
  h2:
    console:
      enabled: true
      path: /h2-console

  # JPA配置 - H2模式（已切换到MySQL）
  # jpa:
  #   hibernate:
  #     ddl-auto: create-drop
  #   show-sql: true
  #   properties:
  #     hibernate:
  #       dialect: org.hibernate.dialect.H2Dialect
  #       format_sql: true

  # JPA配置 - MySQL模式（当前使用）
  jpa:
    hibernate:
      ddl-auto: validate  # 使用validate模式，不自动修改表结构
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.siact.energy.cal: DEBUG
    org.springframework.web: DEBUG
    org.springframework.test: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# LiteFlow配置
liteflow:
  rule-source: xml
  rule-source-ext-data-map:
    url: classpath:flow/test-flow.xml
  enable: false  # 测试环境禁用LiteFlow，避免初始化问题
  
# 测试专用配置
test:
  # 是否启用事务回滚
  transaction-rollback: true
  # 测试数据初始化
  data-init: true
  # 测试超时时间（秒）
  timeout: 30
