-- 检查数据库数据完整性
USE energy_cal_test;

-- 1. 检查 rule_detail 表中的空值情况
SELECT 'Rule Detail Null Values Check' as check_type;

SELECT 
    COUNT(*) as total_records,
    SUM(CASE WHEN rule_formula IS NULL THEN 1 ELSE 0 END) as null_rule_formula_count,
    SUM(CASE WHEN rule_formula = '' THEN 1 ELSE 0 END) as empty_rule_formula_count,
    SUM(CASE WHEN rule_name IS NULL THEN 1 ELSE 0 END) as null_rule_name_count,
    SUM(CASE WHEN dev_code IS NULL THEN 1 ELSE 0 END) as null_dev_code_count,
    SUM(CASE WHEN dev_property IS NULL THEN 1 ELSE 0 END) as null_dev_property_count
FROM rule_detail;

-- 2. 显示有问题的记录
SELECT 'Problematic Records' as check_type;

SELECT 
    id,
    rule_name,
    rule_formula,
    dev_code,
    dev_property,
    active_state,
    CASE 
        WHEN rule_formula IS NULL THEN 'rule_formula is NULL'
        WHEN rule_formula = '' THEN 'rule_formula is EMPTY'
        ELSE 'OK'
    END as issue
FROM rule_detail 
WHERE rule_formula IS NULL OR rule_formula = ''
LIMIT 10;

-- 3. 检查表结构
SELECT 'Table Structure Check' as check_type;
DESCRIBE rule_detail;

-- 4. 更新空的 rule_formula 为默认值（可选）
-- 如果需要修复空值，可以取消注释以下语句
/*
UPDATE rule_detail 
SET rule_formula = '[]' 
WHERE rule_formula IS NULL OR rule_formula = '';

SELECT 'Updated null rule_formula records' as message, ROW_COUNT() as affected_rows;
*/

-- 5. 插入一些测试数据（如果表为空）
INSERT IGNORE INTO rule_detail (
    id, rule_name, rule_des, rule_type, cal_type, 
    dev_code, dev_name, dev_property, prop_name,
    rule_formula, rule_formula_show, 
    project_id, rule_col_id, active_state
) VALUES 
(1, '测试规则1', '测试规则描述1', 0, 1, 
 'TEST_DEV_001', '测试设备1', 'TEST_PROP_001', '测试属性1',
 '["@[TEST_PROP_001]", "+", "100"]', '["测试属性1", "+", "100"]',
 1, 1, 0),
(2, '测试规则2', '测试规则描述2', 0, 1,
 'TEST_DEV_002', '测试设备2', 'TEST_PROP_002', '测试属性2', 
 '["@[TEST_PROP_002]", "*", "2"]', '["测试属性2", "*", "2"]',
 1, 1, 0),
(3, '空公式规则', '用于测试空公式的规则', 0, 1,
 'TEST_DEV_003', '测试设备3', 'TEST_PROP_003', '测试属性3',
 '[]', '[]',
 1, 1, 0);

-- 6. 最终验证
SELECT 'Final Verification' as check_type;
SELECT 
    COUNT(*) as total_records,
    SUM(CASE WHEN rule_formula IS NULL OR rule_formula = '' THEN 1 ELSE 0 END) as problematic_records,
    SUM(CASE WHEN rule_formula IS NOT NULL AND rule_formula != '' THEN 1 ELSE 0 END) as valid_records
FROM rule_detail;
