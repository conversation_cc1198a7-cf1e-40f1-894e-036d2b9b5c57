<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <!-- 测试用的基础流程链配置 -->
    
    <!-- 表达式计算流程链 -->
    <chain name="expressionChain">
        THEN(
            dataSourceNode,
            expressionCalculate,
            resultOutput
        );
    </chain>

    <!-- 流程模式计算流程链 -->
    <chain name="flowChain">
        THEN(
            dataSourceNode,
            flowCalculate,
            resultOutput
        );
    </chain>

    <!-- 测试用的简单流程链 -->
    <chain name="testChain">
        THEN(
            testNode1,
            testNode2
        );
    </chain>

    <!-- 数据源节点流程链 -->
    <chain name="dataSourceChain">
        dataSourceNode;
    </chain>
    
    <!-- 计算节点流程链 -->
    <chain name="calculateChain">
        THEN(
            calculate,
            validate
        );
    </chain>
    
    <!-- 输出节点流程链 -->
    <chain name="outputChain">
        resultOutput;
    </chain>
    
    <!-- 复合流程链示例 -->
    <chain name="complexChain">
        THEN(
            dataSourceNode,
            WHEN(
                condition1,
                expressionCalculate,
                flowCalculate
            ),
            resultOutput
        );
    </chain>
    
</flow>
