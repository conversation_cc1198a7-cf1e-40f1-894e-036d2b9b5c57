package com.siact.energy.cal.common.datasource.config;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * MyBatis配置自定义器
 * 用于设置数据库ID提供者
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Component
public class MyBatisConfigurationCustomizer implements ConfigurationCustomizer {

    @Autowired
    private DatabaseIdProvider databaseIdProvider;

    @Override
    public void customize(MybatisConfiguration configuration) {
        // 设置数据库ID提供者
        // 注意：这里不能直接设置databaseId，需要通过SqlSessionFactory设置
        // 这个方法主要用于其他配置的自定义
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCacheEnabled(false);
    }
}
