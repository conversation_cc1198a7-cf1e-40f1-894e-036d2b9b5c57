-- 测试数据库迁移脚本
-- 将现有的 energy_cal_test 数据库升级到 energy_cal_2.1.1 结构

USE energy_cal_test;

-- 1. 备份现有数据（可选）
-- CREATE TABLE rule_detail_backup AS SELECT * FROM rule_detail;

-- 2. 检查并添加缺失的字段
-- 添加 calculation_mode 字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'energy_cal_test' 
     AND TABLE_NAME = 'rule_detail' 
     AND COLUMN_NAME = 'calculation_mode') = 0,
    'ALTER TABLE rule_detail ADD COLUMN calculation_mode int(11) NOT NULL DEFAULT 0 COMMENT ''计算模式: 0-表达式模式, 1-流程模式'' AFTER rule_des',
    'SELECT ''calculation_mode already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 flow_chain_id 字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'energy_cal_test' 
     AND TABLE_NAME = 'rule_detail' 
     AND COLUMN_NAME = 'flow_chain_id') = 0,
    'ALTER TABLE rule_detail ADD COLUMN flow_chain_id bigint(20) NULL DEFAULT NULL COMMENT ''关联的流程链ID (逻辑外键, 指向 si_liteflow_chain.id)'' AFTER calculation_mode',
    'SELECT ''flow_chain_id already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 latest_version_id 字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'energy_cal_test' 
     AND TABLE_NAME = 'rule_detail' 
     AND COLUMN_NAME = 'latest_version_id') = 0,
    'ALTER TABLE rule_detail ADD COLUMN latest_version_id bigint(20) NULL DEFAULT NULL COMMENT ''最新版本ID (用于版本控制)'' AFTER active_state',
    'SELECT ''latest_version_id already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 修改字段长度和类型（如果需要）
-- 扩展 rule_name 字段长度
ALTER TABLE rule_detail MODIFY COLUMN rule_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '指标规则名称';

-- 扩展 rule_des 字段长度
ALTER TABLE rule_detail MODIFY COLUMN rule_des varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '指标规则描述';

-- 扩展 dev_name 字段长度
ALTER TABLE rule_detail MODIFY COLUMN dev_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联节点(模型)的名称';

-- 扩展 prop_name 字段长度
ALTER TABLE rule_detail MODIFY COLUMN prop_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联属性的名称';

-- 4. 更新表注释
ALTER TABLE rule_detail COMMENT = '指标规则详情表 (支持表达式与流程双模式)';

-- 5. 创建测试数据
-- 清空现有测试数据
DELETE FROM rule_detail WHERE id < 1000;

-- 插入表达式模式测试数据
INSERT INTO rule_detail (
    id, rule_name, rule_des, calculation_mode, flow_chain_id, 
    rule_type, cal_type, dev_code, dev_name, dev_property, prop_name,
    rule_formula, rule_formula_show, project_id, rule_col_id, active_state,
    creator, create_time, updater, update_time, deleted
) VALUES (
    1, '测试表达式规则', '用于测试的表达式模式规则', 0, NULL,
    0, 1, 'TEST_DEV_001', '测试设备', 'TEST_PROP_001', '测试属性',
    '["@[TEST_PROP_001]", "+", "100"]', '["测试属性", "+", "100"]', 
    1, 1, 0, 1, NOW(), 1, NOW(), 0
);

-- 插入流程模式测试数据
INSERT INTO rule_detail (
    id, rule_name, rule_des, calculation_mode, flow_chain_id,
    rule_type, cal_type, dev_code, dev_name, dev_property, prop_name,
    rule_formula, rule_formula_show, project_id, rule_col_id, active_state,
    creator, create_time, updater, update_time, deleted
) VALUES (
    2, '测试流程规则', '用于测试的流程模式规则', 1, 1001,
    0, 1, 'TEST_DEV_002', '测试设备2', 'TEST_PROP_002', '测试属性2',
    NULL, '["流程计算结果"]',
    1, 1, 0, 1, NOW(), 1, NOW(), 0
);

-- 6. 验证迁移结果
SELECT 
    'Migration completed successfully' as status,
    COUNT(*) as total_records,
    SUM(CASE WHEN calculation_mode = 0 THEN 1 ELSE 0 END) as expression_mode_count,
    SUM(CASE WHEN calculation_mode = 1 THEN 1 ELSE 0 END) as flow_mode_count
FROM rule_detail;

-- 显示表结构
DESCRIBE rule_detail;
