package com.siact.energy.cal.tool.common.utils;

import com.siact.energy.cal.tool.service.BaseService;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户上下文工具类
 * 用于获取当前登录用户信息，支持权限系统开启/关闭的场景
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
public class UserContextUtil {

    /**
     * 获取当前登录用户ID
     * 当权限系统关闭时，返回默认用户ID
     *
     * @param baseService BaseService实例
     * @return 当前用户ID，如果无法获取则返回默认值1L
     */
    public static Long getCurrentUserId(BaseService baseService) {
        try {
            // 检查权限系统是否启用
            if (!baseService.isEnabled()) {
                log.debug("权限系统未启用，使用默认用户ID: 1");
                return 1L;
            }

            // 通过反射调用getLoginUser方法（避免类型依赖问题）
            java.lang.reflect.Method getLoginUserMethod = baseService.getClass().getMethod("getLoginUser");
            Object userDetailVO = getLoginUserMethod.invoke(baseService);
            
            if (userDetailVO == null) {
                log.debug("未获取到登录用户信息，使用默认用户ID: 1");
                return 1L;
            }

            // 尝试获取用户ID
            Long userId = extractUserId(userDetailVO);
            log.debug("获取到当前用户ID: {}", userId);
            return userId;
        } catch (Exception e) {
            log.warn("获取当前用户ID失败: {}, 使用默认用户ID: 1", e.getMessage());
            return 1L;
        }
    }

    /**
     * 从UserDetailVO对象中提取用户ID
     * 尝试多种可能的字段名
     *
     * @param userDetailVO 用户详情对象
     * @return 用户ID，如果无法获取则返回默认值1L
     */
    private static Long extractUserId(Object userDetailVO) {
        try {
            Class<?> clazz = userDetailVO.getClass();

            // 尝试常见的用户ID getter方法名
            String[] possibleMethodNames = {"getId", "getUserId", "getUser_id", "getID", "getUSER_ID"};

            // 先尝试通过getter方法获取
            for (String methodName : possibleMethodNames) {
                try {
                    java.lang.reflect.Method method = clazz.getMethod(methodName);
                    Object result = method.invoke(userDetailVO);
                    if (result != null) {
                        if (result instanceof Long) {
                            return (Long) result;
                        } else if (result instanceof Integer) {
                            return ((Integer) result).longValue();
                        } else if (result instanceof String) {
                            return Long.valueOf((String) result);
                        }
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个方法
                }
            }

            return 1L;
        } catch (Exception e) {
            return 1L;
        }
    }

    /**
     * 检查权限系统是否启用
     *
     * @param baseService BaseService实例
     * @return true-启用，false-未启用
     */
    public static boolean isAuthEnabled(BaseService baseService) {
        try {
            return baseService.isEnabled();
        } catch (Exception e) {
            log.warn("检查权限系统状态失败: {}", e.getMessage());
            return false;
        }
    }
}
