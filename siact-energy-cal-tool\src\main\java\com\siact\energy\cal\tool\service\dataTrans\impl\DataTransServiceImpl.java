package com.siact.energy.cal.tool.service.dataTrans.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransSubscribeDTO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransUpdateDTO;
import com.siact.energy.cal.common.pojo.vo.dataTrans.DataTransVO;
import com.siact.energy.cal.common.util.utils.ClassUtil;
import com.siact.energy.cal.tool.common.utils.CustomMqttMessageListener;
import com.siact.energy.cal.tool.common.utils.MqttUtil;
import com.siact.energy.cal.tool.common.utils.SseEmitterUtil;
import com.siact.energy.cal.tool.convertor.dataTrans.DataTransConvertor;
import com.siact.energy.cal.tool.dao.dataTrans.DataTransDao;
import com.siact.energy.cal.tool.entity.dataTrans.DataTrans;
import com.siact.energy.cal.tool.service.dataTrans.DataTransService;
import com.siact.energy.cal.tool.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.Serializable;
import java.util.List;

/**
 * 数据传输表（mqtt）(DataTrans)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-15 11:25:36
 */
@Slf4j
@Service("dataTransService")
public class DataTransServiceImpl extends ServiceImpl<DataTransDao, DataTrans> implements DataTransService {

    @Autowired
    private BaseService baseService;

    @Override
    public PageBean<DataTransVO> listPage(PageBean<DataTransVO> page, DataTransQueryDTO dataTransQueryDTO) {
        return page(page, dataTransQueryDTO);
    }

    @Override
    public DataTransVO getVoById(Serializable id) {
        DataTrans dataTrans = getById(id);
        return DataTransConvertor.INSTANCE.entity2Vo(dataTrans);
    }

    @Override
    public Boolean save(DataTransInsertDTO dataTransInsertDTO) {
        DataTrans dataTrans = DataTransConvertor.INSTANCE.insertDTO2Entity(dataTransInsertDTO);

        // 设置用户信息
        Long currentUserId = getCurrentUserId();
        dataTrans.setCreator(currentUserId);
        dataTrans.setUpdater(currentUserId);

        return save(dataTrans);
    }

    @Override
    public Boolean updateVoById(DataTransUpdateDTO dataTransUpdateDTO) {
        DataTrans dataTrans = DataTransConvertor.INSTANCE.updateDTO2Entity(dataTransUpdateDTO);

        // 设置更新者信息
        Long currentUserId = getCurrentUserId();
        dataTrans.setUpdater(currentUserId);

        return updateById(dataTrans);
    }

    @Override
    public DataTransVO getByProjectId(Long projectId) {

        LambdaQueryWrapper<DataTrans> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.eq(DataTrans::getProjectId, projectId);

        List<DataTrans> dataTransList = list(queryWrapper);
        if(CollectionUtils.isEmpty(dataTransList)) {
            return null;
        }

        return DataTransConvertor.INSTANCE.entity2Vo(dataTransList.get(0));
    }

    @Override
    public Boolean saveOrUpdate(DataTransUpdateDTO dataTransUpdateDTO) {
        return saveOrUpdate(DataTransConvertor.INSTANCE.updateDTO2Entity(dataTransUpdateDTO));
    }

    @Override
    public SseEmitter subscribe(DataTransSubscribeDTO dto) {

        String subscribeId = dto.getSubscribeId();

        // TODO 高卫东 考虑一定时间后自动销毁

        // 存在订阅直接返回错误
        if(MqttUtil.hasSubscribe(subscribeId)|| SseEmitterUtil.hasSseEmitter(subscribeId)) {
            throw new BizException("当前订阅ID对应的订阅关系已经存在");
        }

        // 1. 校验MQTT连接
        MqttClient mqttClient;
        try {
            mqttClient = MqttUtil.createAndConnectMqttClientCache(dto.getHost(), dto.getPort(), dto.getUserName(), dto.getPassword(), subscribeId);
        } catch (MqttException e) {
            log.error("连接MQTT失败，", e);
            throw new BizException("连接MQTT失败");
        }

        // 2.创建SseEmitter
        SseEmitter sseEmitter = SseEmitterUtil.createSseEmitter(subscribeId);
        String topic = dto.getTopic();
        try {
            // 3. 订阅MQTT主题
            MqttUtil.subscribeAndCache(mqttClient, topic, new CustomMqttMessageListener(sseEmitter), subscribeId);
        } catch (MqttException e) {
            log.error("订阅MQTT主题失败 topic: {}，", topic, e);
            // 4. 订阅失败销毁MqttClient
            try {
                // 销毁MQTT客户端
                MqttUtil.destroyMqttClient(subscribeId);
            } catch (MqttException ex) {
                log.error("销毁MqttClient失败", e);
            }
            // 5. 订阅失败销毁SseEmitter
            SseEmitterUtil.destroySseEmitter(subscribeId);
            throw new BizException("订阅MQTT主题失败");
        }

        return sseEmitter;
    }

    @Override
    public Boolean unsubscribe(String subscribeId) {

        // 1. 取消MQTT订阅
        try {
            MqttUtil.unsubscribeAndRemoveCache(subscribeId);
        } catch (MqttException e) {
            log.error("取消MQTT订阅失败 subscribeId: {}", subscribeId, e);
            throw new BizException("取消MQTT订阅失败");
        }

        // 2. 销毁SseEmitter
        SseEmitterUtil.destroySseEmitter(subscribeId);


        // 3. 销毁MqttClient
        try {
            MqttUtil.destroyMqttClient(subscribeId);
        } catch (MqttException e) {
            log.error("销毁MQTT连接客户端失败，", e);
        }

        return Boolean.TRUE;
    }

    /**
     * 分页查询
     *
     * @param page              分页对象
     * @param dataTransQueryDTO 查询实体
     * @return 分页数据
     */
    private PageBean<DataTransVO> page(PageBean<DataTransVO> page, DataTransQueryDTO dataTransQueryDTO) {

        // 转换器
        DataTransConvertor convertor = DataTransConvertor.INSTANCE;
        // VO转实体
        DataTrans dataTrans = convertor.queryDTO2Entity(dataTransQueryDTO);

        // 创建查询对象
        LambdaQueryWrapper<DataTrans> queryWrapper = new LambdaQueryWrapper<>(dataTrans);

        List<String> voFieldNameList = ClassUtil.getClassAllFields(DataTransVO.class);
        queryWrapper.select(c -> voFieldNameList.contains(c.getProperty()));

        // 查询实体数据
        Page<DataTrans> entityPage = page(convertor.voPageBean2EntityPage(page), queryWrapper);

        // 实体分页转VO分页
        PageBean<DataTransVO> voPageBean = convertor.entityPage2VoPageBean(entityPage);

        return voPageBean;
    }

    /**
     * 获取当前登录用户ID
     * 通过BaseService调用权限SDK获取用户信息
     *
     * @return 当前用户ID，如果无法获取则返回默认值1L
     */
    private Long getCurrentUserId() {
        try {
            // 通过反射调用getLoginUser方法
            java.lang.reflect.Method getLoginUserMethod = baseService.getClass().getMethod("getLoginUser");
            Object userDetailVO = getLoginUserMethod.invoke(baseService);

            if (userDetailVO == null) {
                return 1L;
            }

            // 尝试通过反射获取用户ID
            return extractUserId(userDetailVO);
        } catch (Exception e) {
            return 1L;
        }
    }

    /**
     * 从UserDetailVO对象中提取用户ID
     * 尝试多种可能的字段名
     */
    private Long extractUserId(Object userDetailVO) {
        try {
            Class<?> clazz = userDetailVO.getClass();

            // 尝试常见的用户ID getter方法名
            String[] possibleMethodNames = {"getId", "getUserId", "getUser_id", "getID", "getUSER_ID"};

            // 先尝试通过getter方法获取
            for (String methodName : possibleMethodNames) {
                try {
                    java.lang.reflect.Method method = clazz.getMethod(methodName);
                    Object result = method.invoke(userDetailVO);
                    if (result != null) {
                        if (result instanceof Long) {
                            return (Long) result;
                        } else if (result instanceof Integer) {
                            return ((Integer) result).longValue();
                        } else if (result instanceof String) {
                            return Long.valueOf((String) result);
                        }
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个方法
                }
            }

            return 1L;
        } catch (Exception e) {
            return 1L;
        }
    }

}

