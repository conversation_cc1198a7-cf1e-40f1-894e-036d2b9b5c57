-- 验证数据库迁移结果
USE energy_cal_test;

-- 1. 检查表结构
SELECT 'Table Structure Check' as check_type;
DESCRIBE rule_detail;

-- 2. 验证关键字段是否存在
SELECT 'Key Fields Verification' as check_type;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'energy_cal_test' 
AND TABLE_NAME = 'rule_detail'
AND COLUMN_NAME IN ('calculation_mode', 'flow_chain_id', 'latest_version_id')
ORDER BY ORDINAL_POSITION;

-- 3. 检查字段长度是否正确
SELECT 'Field Length Verification' as check_type;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'energy_cal_test' 
AND TABLE_NAME = 'rule_detail'
AND COLUMN_NAME IN ('rule_name', 'rule_des', 'dev_name', 'prop_name')
ORDER BY ORDINAL_POSITION;

-- 4. 验证测试数据
SELECT 'Test Data Verification' as check_type;
SELECT 
    id,
    rule_name,
    calculation_mode,
    flow_chain_id,
    rule_type,
    cal_type,
    active_state,
    create_time
FROM rule_detail 
WHERE id IN (1, 2)
ORDER BY id;

-- 5. 统计数据
SELECT 'Data Statistics' as check_type;
SELECT 
    COUNT(*) as total_records,
    SUM(CASE WHEN calculation_mode = 0 THEN 1 ELSE 0 END) as expression_mode_count,
    SUM(CASE WHEN calculation_mode = 1 THEN 1 ELSE 0 END) as flow_mode_count,
    SUM(CASE WHEN rule_type = 0 THEN 1 ELSE 0 END) as instance_rules,
    SUM(CASE WHEN rule_type = 1 THEN 1 ELSE 0 END) as model_rules,
    SUM(CASE WHEN active_state = 0 THEN 1 ELSE 0 END) as active_rules,
    SUM(CASE WHEN deleted = 0 THEN 1 ELSE 0 END) as non_deleted_rules
FROM rule_detail;

-- 6. 检查索引
SELECT 'Index Verification' as check_type;
SHOW INDEX FROM rule_detail;

-- 7. 验证表注释
SELECT 'Table Comment Verification' as check_type;
SELECT 
    TABLE_NAME,
    TABLE_COMMENT
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'energy_cal_test' 
AND TABLE_NAME = 'rule_detail';

-- 8. 最终验证结果
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
              WHERE TABLE_SCHEMA = 'energy_cal_test' 
              AND TABLE_NAME = 'rule_detail' 
              AND COLUMN_NAME = 'calculation_mode') > 0
        AND (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
             WHERE TABLE_SCHEMA = 'energy_cal_test' 
             AND TABLE_NAME = 'rule_detail' 
             AND COLUMN_NAME = 'flow_chain_id') > 0
        AND (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
             WHERE TABLE_SCHEMA = 'energy_cal_test' 
             AND TABLE_NAME = 'rule_detail' 
             AND COLUMN_NAME = 'latest_version_id') > 0
        AND (SELECT CHARACTER_MAXIMUM_LENGTH FROM INFORMATION_SCHEMA.COLUMNS 
             WHERE TABLE_SCHEMA = 'energy_cal_test' 
             AND TABLE_NAME = 'rule_detail' 
             AND COLUMN_NAME = 'rule_name') = 255
        THEN '✅ Migration Successful - All required fields and modifications are in place'
        ELSE '❌ Migration Failed - Some required changes are missing'
    END as migration_status;
