-- 测试数据初始化脚本
-- 用于支持计算公式配置的CRUD测试

-- 1. 创建测试项目
INSERT INTO data_project (id, project_name, project_desc, create_time, update_time, deleted) 
VALUES (1, '测试项目', '用于测试的项目', NOW(), NOW(), 0);

-- 2. 创建测试指标集
INSERT INTO rule_col (id, col_name, col_desc, project_id, create_time, update_time, deleted)
VALUES (1, '测试指标集', '用于测试的指标集', 1, NOW(), NOW(), 0);

-- 3. 创建测试数据源
INSERT INTO data_source (id, source_name, source_desc, db_type, host, port, database_name, username, password, project_id, create_time, update_time, deleted)
VALUES (1, '测试数据源', '用于测试的数据源', 0, 'localhost', 6030, 'test_db', 'root', 'taosdata', 1, NOW(), NOW(), 0);

-- 4. 创建测试组件
INSERT INTO si_component (id, component_name, component_desc, component_type, create_time, update_time, deleted)
VALUES 
(1, 'DataSourceNode', '数据源组件', 'INPUT', NOW(), NOW(), 0),
(2, 'CommonNode', '通用计算组件', 'PROCESS', NOW(), NOW(), 0),
(3, 'OutPutNode', '输出组件', 'OUTPUT', NOW(), NOW(), 0),
(4, 'SwitchNode', '条件判断组件', 'CONDITION', NOW(), NOW(), 0),
(5, 'DataSourceMQTTNode', 'MQTT数据组件', 'INPUT', NOW(), NOW(), 0);

-- 5. 创建测试流程链
INSERT INTO si_liteflow_chain (id, chain_name, chain_desc, el_data, create_time, update_time, deleted)
VALUES 
(1, '测试流程链1', '用于测试的流程链1', 'THEN(DataSourceNode, CommonNode, OutPutNode)', NOW(), NOW(), 0),
(2, '测试流程链2', '用于测试的流程链2', 'THEN(DataSourceMQTTNode, SwitchNode, CommonNode, OutPutNode)', NOW(), NOW(), 0);

-- 6. 创建测试组件流
INSERT INTO si_component_flow (id, flow_name, flow_desc, flow_content, chain_id, create_time, update_time, deleted)
VALUES 
(1, '测试组件流1', '用于测试的组件流1', '{"nodes":[],"edges":[]}', 1, NOW(), NOW(), 0),
(2, '测试组件流2', '用于测试的组件流2', '{"nodes":[],"edges":[]}', 2, NOW(), NOW(), 0);

-- 7. 创建测试规则详情（表达式模式）
INSERT INTO rule_detail (
    id, rule_name, rule_des, calculation_mode, rule_type, cal_type, 
    dev_code, dev_name, dev_property, prop_name, 
    rule_formula, rule_formula_show, 
    project_id, rule_col_id, active_state, 
    create_time, update_time, deleted
) VALUES (
    1, '测试表达式规则1', '用于测试的表达式规则', 0, 0, 1,
    'TEST_DEV_001', '测试设备1', 'TEST_PROP_001', '测试属性1',
    '["@[VAR1]", "+", "@[VAR2]"]', '["变量1", "+", "变量2"]',
    1, 1, 1,
    NOW(), NOW(), 0
);

-- 8. 创建测试规则详情（流程模式）
INSERT INTO rule_detail (
    id, rule_name, rule_des, calculation_mode, rule_type, cal_type,
    dev_code, dev_name, dev_property, prop_name,
    flow_chain_id,
    project_id, rule_col_id, active_state,
    create_time, update_time, deleted
) VALUES (
    2, '测试流程规则1', '用于测试的流程规则', 1, 0, 1,
    'TEST_DEV_002', '测试设备2', 'TEST_PROP_002', '测试属性2',
    1,
    1, 1, 1,
    NOW(), NOW(), 0
);

-- 9. 创建测试规则实例
INSERT INTO rule_detail_instance (
    id, rule_detail_id, instance_code, instance_name, instance_desc,
    create_time, update_time, deleted
) VALUES 
(1, 1, 'INSTANCE_001', '测试实例1', '表达式规则的测试实例', NOW(), NOW(), 0),
(2, 2, 'INSTANCE_002', '测试实例2', '流程规则的测试实例', NOW(), NOW(), 0);

-- 10. 创建测试计算类型字典
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_time, remark)
VALUES 
('cal_type', 1, '基础指标', '1', 'cal_type', '', 'primary', 'Y', '0', NOW(), '基础指标计算'),
('cal_type', 2, '聚合指标', '2', 'cal_type', '', 'success', 'N', '0', NOW(), '聚合指标计算'),
('cal_type', 3, '衍生指标', '3', 'cal_type', '', 'info', 'N', '0', NOW(), '衍生指标计算');

-- 11. 创建测试规则类型字典
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_time, remark)
VALUES 
('rule_type', 1, '实例级规则', '0', 'rule_type', '', 'primary', 'Y', '0', NOW(), '实例级规则'),
('rule_type', 2, '模型级规则', '1', 'rule_type', '', 'warning', 'N', '0', NOW(), '模型级规则');

-- 12. 创建测试计算模式字典
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_time, remark)
VALUES 
('calculation_mode', 1, '表达式模式', '0', 'calculation_mode', '', 'primary', 'Y', '0', NOW(), '基于表达式的计算模式'),
('calculation_mode', 2, '流程模式', '1', 'calculation_mode', '', 'success', 'N', '0', NOW(), '基于流程的计算模式');

-- 13. 创建测试用户（如果需要）
INSERT INTO sys_user (user_id, user_name, nick_name, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_time, remark)
VALUES (1, 'testuser', '测试用户', '<EMAIL>', '13800138000', '0', '', '$2a$10$7JB720yubVSOfvVMe6/b.eRh4QfVDdh5VTkZHtVPpyLfGdDgIYrjG', '0', '0', '127.0.0.1', NOW(), NOW(), '测试用户账号');

-- 14. 创建测试角色
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_time, remark)
VALUES (1, '测试角色', 'test', 1, '1', 1, 1, '0', '0', NOW(), '测试角色');

-- 15. 用户角色关联
INSERT INTO sys_user_role (user_id, role_id) VALUES (1, 1);

-- 提交事务
COMMIT;
