# MySQL测试环境配置
server:
  port: 8081

spring:
  application:
    name: siact-energy-cal-tool-mysql-test
  
  # MySQL数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************************************
    username: root
    password: root
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      pool-name: TestHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop  # 测试环境每次重新创建表
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        hbm2ddl:
          auto: create-drop

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.siact.energy.cal: DEBUG
    org.springframework.web: DEBUG
    org.springframework.test: DEBUG
    org.springframework.jdbc: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# LiteFlow配置
liteflow:
  rule-source: xml
  rule-source-ext-data-map:
    url: classpath:flow/test-flow.xml
  enable: true
  
# 测试专用配置
test:
  # 是否启用事务回滚
  transaction-rollback: true
  # 测试数据初始化
  data-init: true
  # 测试超时时间（秒）
  timeout: 60
  # 数据库类型
  database-type: mysql
