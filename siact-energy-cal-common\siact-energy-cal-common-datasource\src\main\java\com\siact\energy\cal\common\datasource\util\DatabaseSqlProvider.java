package com.siact.energy.cal.common.datasource.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 数据库SQL提供者
 * 根据不同数据库类型提供相应的SQL语句
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Component
public class DatabaseSqlProvider {

    @Autowired
    private DatabaseCompatibilityUtil databaseCompatibilityUtil;

    /**
     * 获取批量插入或更新的SQL语句模板
     * 
     * @param tableName 表名
     * @param columns 列名数组
     * @param updateColumns 需要更新的列名数组
     * @return SQL语句模板
     */
    public String getInsertOrUpdateBatchSql(String tableName, String[] columns, String[] updateColumns) {
        if (databaseCompatibilityUtil.isMysql()) {
            return getMysqlInsertOrUpdateBatchSql(tableName, columns, updateColumns);
        } else if (databaseCompatibilityUtil.isPostgreSQL()) {
            return getPostgreSqlInsertOrUpdateBatchSql(tableName, columns, updateColumns);
        } else if (databaseCompatibilityUtil.isOracle()) {
            return getOracleInsertOrUpdateBatchSql(tableName, columns, updateColumns);
        } else {
            // 默认使用MySQL语法
            return getMysqlInsertOrUpdateBatchSql(tableName, columns, updateColumns);
        }
    }

    /**
     * MySQL的INSERT ... ON DUPLICATE KEY UPDATE语法
     */
    private String getMysqlInsertOrUpdateBatchSql(String tableName, String[] columns, String[] updateColumns) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");
        
        // 添加列名
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append(columns[i]);
        }
        
        sql.append(") VALUES ");
        sql.append("<foreach collection=\"entities\" item=\"entity\" separator=\",\">");
        sql.append("(");
        
        // 添加参数占位符
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append("#{entity.").append(toCamelCase(columns[i])).append("}");
        }
        
        sql.append(")");
        sql.append("</foreach>");
        sql.append(" ON DUPLICATE KEY UPDATE ");
        
        // 添加更新子句
        for (int i = 0; i < updateColumns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append(updateColumns[i]).append(" = VALUES(").append(updateColumns[i]).append(")");
        }
        
        return sql.toString();
    }

    /**
     * PostgreSQL的INSERT ... ON CONFLICT ... DO UPDATE语法
     */
    private String getPostgreSqlInsertOrUpdateBatchSql(String tableName, String[] columns, String[] updateColumns) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");
        
        // 添加列名
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append(columns[i]);
        }
        
        sql.append(") VALUES ");
        sql.append("<foreach collection=\"entities\" item=\"entity\" separator=\",\">");
        sql.append("(");
        
        // 添加参数占位符
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append("#{entity.").append(toCamelCase(columns[i])).append("}");
        }
        
        sql.append(")");
        sql.append("</foreach>");
        sql.append(" ON CONFLICT (id) DO UPDATE SET ");
        
        // 添加更新子句
        for (int i = 0; i < updateColumns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append(updateColumns[i]).append(" = EXCLUDED.").append(updateColumns[i]);
        }
        
        return sql.toString();
    }

    /**
     * Oracle的MERGE语法
     */
    private String getOracleInsertOrUpdateBatchSql(String tableName, String[] columns, String[] updateColumns) {
        // Oracle的MERGE语法比较复杂，这里提供一个简化版本
        // 实际使用中可能需要根据具体需求调整
        StringBuilder sql = new StringBuilder();
        sql.append("MERGE INTO ").append(tableName).append(" t1 ");
        sql.append("USING (");
        sql.append("<foreach collection=\"entities\" item=\"entity\" separator=\" UNION ALL \">");
        sql.append("SELECT ");
        
        // 添加参数占位符
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append("#{entity.").append(toCamelCase(columns[i])).append("} AS ").append(columns[i]);
        }
        
        sql.append(" FROM DUAL");
        sql.append("</foreach>");
        sql.append(") t2 ON (t1.id = t2.id) ");
        sql.append("WHEN MATCHED THEN UPDATE SET ");
        
        // 添加更新子句
        for (int i = 0; i < updateColumns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append("t1.").append(updateColumns[i]).append(" = t2.").append(updateColumns[i]);
        }
        
        sql.append(" WHEN NOT MATCHED THEN INSERT (");
        
        // 添加插入列名
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append(columns[i]);
        }
        
        sql.append(") VALUES (");
        
        // 添加插入值
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append("t2.").append(columns[i]);
        }
        
        sql.append(")");
        
        return sql.toString();
    }

    /**
     * 将下划线命名转换为驼峰命名
     */
    private String toCamelCase(String columnName) {
        if (columnName == null || columnName.isEmpty()) {
            return columnName;
        }
        
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        
        for (int i = 0; i < columnName.length(); i++) {
            char c = columnName.charAt(i);
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        
        return result.toString();
    }

    /**
     * 获取数据库特定的分页SQL
     */
    public String getPaginationSql(String originalSql, long offset, long limit) {
        if (databaseCompatibilityUtil.isMysql()) {
            return originalSql + " LIMIT " + offset + ", " + limit;
        } else if (databaseCompatibilityUtil.isPostgreSQL()) {
            return originalSql + " LIMIT " + limit + " OFFSET " + offset;
        } else if (databaseCompatibilityUtil.isOracle()) {
            return "SELECT * FROM (SELECT ROWNUM rn, t.* FROM (" + originalSql + ") t WHERE ROWNUM <= " + (offset + limit) + ") WHERE rn > " + offset;
        } else {
            // 默认使用MySQL语法
            return originalSql + " LIMIT " + offset + ", " + limit;
        }
    }

    /**
     * 获取当前时间函数
     */
    public String getCurrentTimestampFunction() {
        if (databaseCompatibilityUtil.isMysql()) {
            return "NOW()";
        } else if (databaseCompatibilityUtil.isPostgreSQL()) {
            return "CURRENT_TIMESTAMP";
        } else if (databaseCompatibilityUtil.isOracle()) {
            return "SYSDATE";
        } else {
            return "NOW()";
        }
    }

    /**
     * 获取字符串连接函数
     */
    public String getConcatFunction(String... columns) {
        if (databaseCompatibilityUtil.isMysql()) {
            return "CONCAT(" + String.join(", ", columns) + ")";
        } else if (databaseCompatibilityUtil.isPostgreSQL()) {
            return String.join(" || ", columns);
        } else if (databaseCompatibilityUtil.isOracle()) {
            return String.join(" || ", columns);
        } else {
            return "CONCAT(" + String.join(", ", columns) + ")";
        }
    }
}
