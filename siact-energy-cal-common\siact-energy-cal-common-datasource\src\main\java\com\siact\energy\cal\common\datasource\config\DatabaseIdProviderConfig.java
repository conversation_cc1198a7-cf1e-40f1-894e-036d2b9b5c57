package com.siact.energy.cal.common.datasource.config;

import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * 数据库ID提供者配置
 * 用于MyBatis根据不同数据库类型选择相应的SQL语句
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Configuration
public class DatabaseIdProviderConfig {

    @Bean
    public DatabaseIdProvider databaseIdProvider() {
        VendorDatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties properties = new Properties();
        
        // 配置数据库厂商标识映射
        properties.setProperty("MySQL", "mysql");
        properties.setProperty("PostgreSQL", "postgresql");
        properties.setProperty("Oracle", "oracle");
        properties.setProperty("SQL Server", "sqlserver");
        properties.setProperty("Microsoft SQL Server", "sqlserver");
        properties.setProperty("H2", "h2");
        properties.setProperty("HSQL Database Engine", "hsqldb");
        properties.setProperty("Apache Derby", "derby");
        properties.setProperty("SQLite", "sqlite");
        properties.setProperty("DB2", "db2");
        properties.setProperty("Informix Dynamic Server", "informix");
        
        // 国产数据库支持
        properties.setProperty("DM DBMS", "dm");  // 达梦数据库
        properties.setProperty("KingbaseES", "postgresql");  // 人大金仓，使用PostgreSQL语法
        properties.setProperty("GBase", "postgresql");  // 南大通用，使用PostgreSQL语法

        // 针对南大通用数据库的特殊处理
        // 南大通用数据库基于PostgreSQL，但可能有不同的产品名称标识
        properties.setProperty("GBase 8a", "postgresql");
        properties.setProperty("GBase 8s", "postgresql");
        properties.setProperty("GBase 8c", "postgresql");
        
        databaseIdProvider.setProperties(properties);
        return databaseIdProvider;
    }
}
