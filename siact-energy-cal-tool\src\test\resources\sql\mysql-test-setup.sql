-- MySQL测试数据库初始化脚本
-- 创建测试数据库和用户

-- 1. 创建测试数据库
DROP DATABASE IF EXISTS siact_energy_cal_test;
CREATE DATABASE siact_energy_cal_test 
  DEFAULT CHARACTER SET utf8mb4 
  DEFAULT COLLATE utf8mb4_unicode_ci;

-- 2. 创建测试用户（可选）
-- DROP USER IF EXISTS 'test_user'@'localhost';
-- CREATE USER 'test_user'@'localhost' IDENTIFIED BY 'test_password';
-- GRANT ALL PRIVILEGES ON siact_energy_cal_test.* TO 'test_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 3. 使用测试数据库
USE siact_energy_cal_test;

-- 4. 设置MySQL参数
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- 注意：实际的表结构会由Hibernate自动创建（ddl-auto: create-drop）
-- 这里只是确保数据库环境正确设置

-- 5. 创建一些测试需要的基础配置表（如果Hibernate没有自动创建）
-- 这些表通常由应用启动时自动创建，这里作为备用

-- 系统字典类型表
CREATE TABLE IF NOT EXISTS sys_dict_type (
  dict_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  dict_name VARCHAR(100) DEFAULT '' COMMENT '字典名称',
  dict_type VARCHAR(100) DEFAULT '' COMMENT '字典类型',
  status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
  create_time DATETIME COMMENT '创建时间',
  update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
  update_time DATETIME COMMENT '更新时间',
  remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (dict_id),
  UNIQUE KEY dict_type (dict_type)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='字典类型表';

-- 系统字典数据表
CREATE TABLE IF NOT EXISTS sys_dict_data (
  dict_code BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  dict_sort INT(4) DEFAULT 0 COMMENT '字典排序',
  dict_label VARCHAR(100) DEFAULT '' COMMENT '字典标签',
  dict_value VARCHAR(100) DEFAULT '' COMMENT '字典键值',
  dict_type VARCHAR(100) DEFAULT '' COMMENT '字典类型',
  css_class VARCHAR(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  list_class VARCHAR(100) DEFAULT NULL COMMENT '表格回显样式',
  is_default CHAR(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
  create_time DATETIME COMMENT '创建时间',
  update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
  update_time DATETIME COMMENT '更新时间',
  remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (dict_code)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='字典数据表';

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建结果
SHOW TABLES;
SELECT 'MySQL测试数据库初始化完成' AS message;
