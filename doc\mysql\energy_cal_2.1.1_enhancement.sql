-- ===========================================
-- SIACT能耗计算平台 - 流程模式增强建议
-- ===========================================

-- 1. 增强组件定义表
ALTER TABLE `si_component` ADD COLUMN `input_params` JSON COMMENT '组件输入参数定义';
ALTER TABLE `si_component` ADD COLUMN `output_params` JSON COMMENT '组件输出参数定义';
ALTER TABLE `si_component` ADD COLUMN `component_class` VARCHAR(255) COMMENT '组件实现类名';
ALTER TABLE `si_component` ADD COLUMN `icon` VARCHAR(100) COMMENT '组件图标';
ALTER TABLE `si_component` ADD COLUMN `category` VARCHAR(50) COMMENT '组件分类';
ALTER TABLE `si_component` ADD COLUMN `version` VARCHAR(20) DEFAULT '1.0.0' COMMENT '组件版本';
ALTER TABLE `si_component` ADD COLUMN `enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用';

-- 2. 新增流程模板表
CREATE TABLE `si_flow_template` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `template_desc` VARCHAR(500) COMMENT '模板描述',
  `category` VARCHAR(50) COMMENT '模板分类',
  `flow_definition` JSON COMMENT '流程定义JSON',
  `preview_image` VARCHAR(255) COMMENT '预览图片路径',
  `usage_count` INT DEFAULT 0 COMMENT '使用次数',
  `creator` BIGINT(20) COMMENT '创建者',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updater` BIGINT(20) COMMENT '更新者', 
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` TINYINT(1) DEFAULT 0 COMMENT '删除标志',
  PRIMARY KEY (`id`)
) COMMENT='流程模板表';

-- 3. 增强流程执行历史表
CREATE TABLE `si_flow_execution_history` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `rule_detail_id` BIGINT(20) NOT NULL COMMENT '关联规则ID',
  `flow_chain_id` BIGINT(20) NOT NULL COMMENT '流程链ID',
  `execution_id` VARCHAR(64) NOT NULL COMMENT '执行ID',
  `status` TINYINT(1) NOT NULL COMMENT '执行状态: 0-运行中, 1-成功, 2-失败, 3-取消',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `end_time` DATETIME COMMENT '结束时间',
  `duration_ms` BIGINT COMMENT '执行耗时(毫秒)',
  `input_data` JSON COMMENT '输入数据',
  `output_data` JSON COMMENT '输出数据',
  `error_message` TEXT COMMENT '错误信息',
  `node_execution_details` JSON COMMENT '节点执行详情',
  PRIMARY KEY (`id`),
  INDEX `idx_rule_detail_id` (`rule_detail_id`),
  INDEX `idx_execution_id` (`execution_id`),
  INDEX `idx_start_time` (`start_time`)
) COMMENT='流程执行历史表';

-- 4. 组件参数配置表
CREATE TABLE `si_component_config` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `flow_id` BIGINT(20) NOT NULL COMMENT '流程ID',
  `node_id` VARCHAR(64) NOT NULL COMMENT '节点ID',
  `component_id` BIGINT(20) NOT NULL COMMENT '组件ID',
  `config_data` JSON NOT NULL COMMENT '配置数据',
  `validation_rules` JSON COMMENT '参数验证规则',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_flow_node` (`flow_id`, `node_id`),
  INDEX `idx_component_id` (`component_id`)
) COMMENT='组件参数配置表';

-- 5. 流程版本管理表
CREATE TABLE `si_flow_version` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `flow_chain_id` BIGINT(20) NOT NULL COMMENT '流程链ID',
  `version_number` VARCHAR(20) NOT NULL COMMENT '版本号',
  `version_desc` VARCHAR(500) COMMENT '版本描述',
  `flow_definition` JSON NOT NULL COMMENT '流程定义快照',
  `is_active` TINYINT(1) DEFAULT 0 COMMENT '是否为活跃版本',
  `creator` BIGINT(20) COMMENT '创建者',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_flow_version` (`flow_chain_id`, `version_number`),
  INDEX `idx_flow_chain_id` (`flow_chain_id`)
) COMMENT='流程版本管理表';

-- 6. 组件依赖关系表
CREATE TABLE `si_component_dependency` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `component_id` BIGINT(20) NOT NULL COMMENT '组件ID',
  `dependency_component_id` BIGINT(20) NOT NULL COMMENT '依赖的组件ID',
  `dependency_type` TINYINT(1) NOT NULL COMMENT '依赖类型: 1-强依赖, 2-弱依赖',
  `version_constraint` VARCHAR(50) COMMENT '版本约束',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_component_dependency` (`component_id`, `dependency_component_id`)
) COMMENT='组件依赖关系表';

-- 7. 数据源映射增强
ALTER TABLE `data_source` ADD COLUMN `connection_pool_config` JSON COMMENT '连接池配置';
ALTER TABLE `data_source` ADD COLUMN `query_timeout` INT DEFAULT 30 COMMENT '查询超时时间(秒)';
ALTER TABLE `data_source` ADD COLUMN `max_retry_count` INT DEFAULT 3 COMMENT '最大重试次数';
ALTER TABLE `data_source` ADD COLUMN `health_check_sql` VARCHAR(200) COMMENT '健康检查SQL';

-- 8. 规则详情表增强索引
ALTER TABLE `rule_detail` ADD INDEX `idx_calculation_mode` (`calculation_mode`);
ALTER TABLE `rule_detail` ADD INDEX `idx_flow_chain_id` (`flow_chain_id`);
ALTER TABLE `rule_detail` ADD INDEX `idx_project_rule_col` (`project_id`, `rule_col_id`);

-- 9. 组件执行日志表
CREATE TABLE `si_component_execution_log` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `execution_id` VARCHAR(64) NOT NULL COMMENT '执行ID',
  `flow_chain_id` BIGINT(20) NOT NULL COMMENT '流程链ID',
  `node_id` VARCHAR(64) NOT NULL COMMENT '节点ID',
  `component_id` BIGINT(20) NOT NULL COMMENT '组件ID',
  `status` TINYINT(1) NOT NULL COMMENT '执行状态',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `end_time` DATETIME COMMENT '结束时间',
  `duration_ms` BIGINT COMMENT '执行耗时',
  `input_data` JSON COMMENT '输入数据',
  `output_data` JSON COMMENT '输出数据',
  `error_message` TEXT COMMENT '错误信息',
  `memory_usage` BIGINT COMMENT '内存使用量(字节)',
  `cpu_time_ms` BIGINT COMMENT 'CPU时间(毫秒)',
  PRIMARY KEY (`id`),
  INDEX `idx_execution_id` (`execution_id`),
  INDEX `idx_flow_chain_id` (`flow_chain_id`),
  INDEX `idx_start_time` (`start_time`)
) COMMENT='组件执行日志表';
