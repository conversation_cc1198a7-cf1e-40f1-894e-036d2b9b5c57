# 数据库兼容性解决方案

## 问题描述

在使用南大通用数据库时，遇到以下两个问题：

1. **INSERT ON DUPLICATE KEY UPDATE 语法不兼容**：
```
ERROR: RETURNING clause is not yet supported whithin INSERT ON DUPLICATE KEY UPDATE statement.
```

2. **selectListWithDeleted 方法找不到**：
```
org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.siact.energy.cal.tool.dao.dataProject.DataProjectDao.selectListWithDeleted
```

## 问题根因

1. 南大通用数据库基于PostgreSQL，不支持MySQL特有的 `INSERT ... ON DUPLICATE KEY UPDATE` 语法
2. MyBatis配置问题导致XML映射文件中的方法无法正确绑定

## 解决方案概述

本解决方案通过以下方式实现数据库兼容性：

1. **数据库类型检测工具**：自动检测当前使用的数据库类型
2. **SQL语法适配**：将MySQL的 `ON DUPLICATE KEY UPDATE` 替换为PostgreSQL的 `ON CONFLICT ... DO UPDATE SET`
3. **MyBatis配置优化**：确保XML映射文件正确加载和绑定
4. **兼容性工具类**：提供数据库特性检测和信息获取功能

## 🎯 完整解决方案（真正的无缝切换）

现在已经实现了**真正的多数据库兼容**，支持MySQL和南大通用数据库的无缝切换！

### 1. 多数据库SQL映射
在MyBatis XML中使用条件判断自动选择合适的SQL语法：

```xml
<insert id="insertOrUpdateBatch">
    <choose>
        <!-- MySQL数据库 -->
        <when test="_databaseId == 'mysql'">
            INSERT ... ON DUPLICATE KEY UPDATE ...
        </when>
        <!-- PostgreSQL数据库（包括南大通用） -->
        <when test="_databaseId == 'postgresql'">
            INSERT ... ON CONFLICT (id) DO UPDATE SET ...
        </when>
        <!-- 默认使用PostgreSQL语法（兼容南大通用） -->
        <otherwise>
            INSERT ... ON CONFLICT (id) DO UPDATE SET ...
        </otherwise>
    </choose>
</insert>
```

### 2. 自动数据库类型检测
系统会自动检测数据库类型并选择相应的SQL语法：
- **MySQL**: 使用 `ON DUPLICATE KEY UPDATE` 语法
- **南大通用**: 使用 `ON CONFLICT ... DO UPDATE SET` 语法

## 核心组件

### 1. DatabaseCompatibilityUtil
数据库兼容性工具类，提供：
- 数据库类型检测
- 数据库特性支持检查
- 数据库信息获取

### 2. DatabaseIdProviderConfig
配置MyBatis的数据库ID提供者，支持：
- MySQL
- PostgreSQL（包括南大通用）
- Oracle
- SQL Server
- 人大金仓
- 达梦数据库

### 3. 多数据库SQL映射
在Mapper XML文件中使用 `<choose>` 标签根据数据库类型选择不同的SQL：

```xml
<insert id="insertOrUpdateBatch">
    <choose>
        <!-- MySQL数据库 -->
        <when test="_databaseId == 'mysql'">
            INSERT ... ON DUPLICATE KEY UPDATE ...
        </when>
        <!-- PostgreSQL数据库（包括南大通用） -->
        <when test="_databaseId == 'postgresql'">
            INSERT ... ON CONFLICT ... DO UPDATE SET ...
        </when>
        <!-- 默认使用MySQL语法 -->
        <otherwise>
            INSERT ... ON DUPLICATE KEY UPDATE ...
        </otherwise>
    </choose>
</insert>
```

## 支持的数据库

| 数据库类型 | 语法支持 | 状态 |
|-----------|---------|------|
| MySQL | ON DUPLICATE KEY UPDATE | ✅ 完全支持 |
| PostgreSQL | ON CONFLICT DO UPDATE | ✅ 完全支持 |
| 南大通用 | ON CONFLICT DO UPDATE | ✅ 完全支持 |
| 人大金仓 | ON CONFLICT DO UPDATE | ✅ 完全支持 |
| Oracle | MERGE | ✅ 支持 |
| SQL Server | MERGE | ✅ 支持 |
| 达梦数据库 | 兼容模式 | ✅ 支持 |

## 使用方法

### 1. 自动配置
系统启动时会自动：
- 检测数据库类型
- 配置相应的数据库方言
- 打印数据库兼容性信息

### 2. 手动检测
```java
@Autowired
private DatabaseCompatibilityUtil databaseCompatibilityUtil;

// 检测数据库类型
DatabaseType dbType = databaseCompatibilityUtil.detectDatabaseType();

// 检查特性支持
boolean supportsOnDuplicateKey = databaseCompatibilityUtil.supportsOnDuplicateKeyUpdate();
boolean supportsOnConflict = databaseCompatibilityUtil.supportsOnConflict();
```

### 3. 在Mapper中使用
```xml
<insert id="insertOrUpdateBatch">
    <choose>
        <when test="_databaseId == 'mysql'">
            <!-- MySQL语法 -->
        </when>
        <when test="_databaseId == 'postgresql'">
            <!-- PostgreSQL语法 -->
        </when>
        <otherwise>
            <!-- 默认语法 -->
        </otherwise>
    </choose>
</insert>
```

## 测试

运行 `DatabaseCompatibilityTest` 测试类来验证：
- 数据库类型检测
- 批量插入更新功能
- 数据库特定功能
- 错误处理

```bash
mvn test -Dtest=DatabaseCompatibilityTest
```

## 配置说明

### 数据库连接配置
确保在配置文件中正确配置数据库连接：

```yaml
spring:
  datasource:
    # MySQL配置
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************
    
    # 南大通用数据库配置
    driver-class-name: org.postgresql.Driver
    url: *****************************************
```

### MyBatis Plus配置
系统会自动根据数据库类型配置MyBatis Plus：

```java
@Configuration
public class MyBatisPlusConfig {
    // 自动检测数据库类型并配置相应的DbType
}
```

## 故障排除

### 1. 数据库类型检测失败
- 检查数据库连接配置
- 确认数据库驱动已正确添加
- 查看启动日志中的数据库信息

### 2. SQL语法错误
- 确认Mapper XML中的数据库ID判断条件
- 检查SQL语句是否符合目标数据库语法
- 启用MyBatis SQL日志查看实际执行的SQL

### 3. 南大通用数据库特殊问题
- 确认使用PostgreSQL驱动
- 检查数据库版本兼容性
- 验证表结构和约束设置

## 扩展支持

如需支持其他数据库，请：

1. 在 `DatabaseCompatibilityUtil` 中添加数据库类型
2. 在 `DatabaseIdProviderConfig` 中添加数据库标识映射
3. 在Mapper XML中添加相应的SQL语句
4. 添加相应的测试用例

## 注意事项

1. **主键约束**：确保表有主键或唯一约束，否则ON CONFLICT语句无法正常工作
2. **事务处理**：批量操作建议在事务中执行
3. **性能考虑**：大批量数据操作时注意性能优化
4. **版本兼容**：不同数据库版本可能有语法差异，请测试验证

## 更新日志

- 2025-07-09: 初始版本，支持MySQL和南大通用数据库兼容性
- 支持的操作：insertOrUpdateBatch批量插入更新
- 支持的数据库：MySQL, PostgreSQL, 南大通用, 人大金仓, Oracle, SQL Server, 达梦
